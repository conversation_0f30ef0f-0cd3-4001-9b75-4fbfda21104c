"""
简单的LSTM示例 - 快速测试用
"""
import torch
import torch.nn as nn
import numpy as np


class SimpleLSTM(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super(SimpleLSTM, self).__init__()
        self.hidden_size = hidden_size
        
        self.lstm = nn.LSTM(input_size, hidden_size, batch_first=True)
        self.fc = nn.Linear(hidden_size, output_size)
        
    def forward(self, x):
        # 初始化隐藏状态
        h0 = torch.zeros(1, x.size(0), self.hidden_size)
        c0 = torch.zeros(1, x.size(0), self.hidden_size)
        
        # LSTM前向传播
        out, _ = self.lstm(x, (h0, c0))
        
        # 取最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        return out


def quick_test():
    """快速测试LSTM"""
    print("PyTorch版本:", torch.__version__)
    print("CUDA可用:", torch.cuda.is_available())
    
    # 创建简单的测试数据
    batch_size = 3
    seq_length = 5
    input_size = 2
    
    # 随机输入数据
    x = torch.randn(batch_size, seq_length, input_size)
    print(f"输入形状: {x.shape}")
    
    # 创建模型
    model = SimpleLSTM(input_size=input_size, hidden_size=10, output_size=1)
    
    # 前向传播
    output = model(x)
    print(f"输出形状: {output.shape}")
    print(f"输出值: {output}")
    
    # 计算参数数量
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型参数总数: {total_params}")
    
    # 测试梯度计算
    target = torch.randn(batch_size, 1)
    criterion = nn.MSELoss()
    loss = criterion(output, target)
    
    print(f"损失值: {loss.item():.4f}")
    
    # 反向传播
    loss.backward()
    print("梯度计算完成!")
    
    # 检查梯度
    for name, param in model.named_parameters():
        if param.grad is not None:
            print(f"{name}: 梯度范数 = {param.grad.norm().item():.4f}")


if __name__ == "__main__":
    quick_test()
