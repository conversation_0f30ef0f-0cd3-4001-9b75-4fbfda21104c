import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, TensorDataset
import unittest


class SimpleLSTM(nn.Module):
    """简单的LSTM模型用于测试"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(SimpleLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM层
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout if num_layers > 1 else 0)

        # 全连接层
        self.fc = nn.Linear(hidden_size, output_size)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # 初始化隐藏状态
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)

        # LSTM前向传播
        out, (hn, cn) = self.lstm(x, (h0, c0))

        # 取最后一个时间步的输出
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)

        return out


class BiLSTM(nn.Module):
    """双向LSTM模型"""

    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout=0.2):
        super(BiLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # 双向LSTM
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, bidirectional=True,
                           dropout=dropout if num_layers > 1 else 0)

        # 全连接层 (双向所以是hidden_size * 2)
        self.fc = nn.Linear(hidden_size * 2, output_size)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # 初始化隐藏状态 (双向所以是num_layers * 2)
        h0 = torch.zeros(self.num_layers * 2, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * 2, x.size(0), self.hidden_size).to(x.device)

        # LSTM前向传播
        out, (hn, cn) = self.lstm(x, (h0, c0))

        # 取最后一个时间步的输出
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)

        return out


def generate_sine_data(seq_length, num_samples, noise_level=0.1):
    """生成正弦波数据用于测试"""
    X = []
    y = []

    for _ in range(num_samples):
        # 随机起始点
        start = np.random.uniform(0, 4 * np.pi)
        # 生成序列
        x_seq = np.linspace(start, start + 2 * np.pi, seq_length + 1)
        sine_seq = np.sin(x_seq) + np.random.normal(0, noise_level, seq_length + 1)

        X.append(sine_seq[:-1])  # 输入序列
        y.append(sine_seq[-1])   # 预测目标

    return np.array(X), np.array(y)


def generate_sequence_classification_data(seq_length, num_samples, num_classes=3):
    """生成序列分类数据"""
    X = []
    y = []

    for _ in range(num_samples):
        # 生成随机序列
        sequence = np.random.randn(seq_length, 1)

        # 根据序列特征确定类别
        if np.mean(sequence) > 0.5:
            label = 0
        elif np.mean(sequence) < -0.5:
            label = 1
        else:
            label = 2

        X.append(sequence)
        y.append(label)

    return np.array(X), np.array(y)


def train_model(model, train_loader, criterion, optimizer, num_epochs, device):
    """训练模型"""
    model.train()
    losses = []

    for epoch in range(num_epochs):
        epoch_loss = 0.0
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)

            # 前向传播
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)

            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            epoch_loss += loss.item()

        avg_loss = epoch_loss / len(train_loader)
        losses.append(avg_loss)

        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {avg_loss:.4f}')

    return losses


def evaluate_model(model, test_loader, criterion, device):
    """评估模型"""
    model.eval()
    total_loss = 0.0
    correct = 0
    total = 0

    with torch.no_grad():
        for batch_x, batch_y in test_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)

            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            total_loss += loss.item()

            # 计算准确率（如果是分类任务）
            if len(outputs.shape) > 1 and outputs.shape[1] > 1:
                _, predicted = torch.max(outputs.data, 1)
                total += batch_y.size(0)
                correct += (predicted == batch_y).sum().item()

    avg_loss = total_loss / len(test_loader)
    accuracy = 100 * correct / total if total > 0 else 0

    return avg_loss, accuracy


def test_regression_task():
    """测试回归任务 - 正弦波预测"""
    print("=" * 50)
    print("测试1: LSTM回归任务 - 正弦波预测")
    print("=" * 50)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    # 生成数据
    seq_length = 20
    X, y = generate_sine_data(seq_length, 1000, noise_level=0.1)

    # 转换为张量
    X = torch.FloatTensor(X).unsqueeze(-1)  # 添加特征维度
    y = torch.FloatTensor(y)

    # 划分训练集和测试集
    train_size = int(0.8 * len(X))
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # 创建数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    test_dataset = TensorDataset(X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    # 创建模型
    model = SimpleLSTM(input_size=1, hidden_size=64, num_layers=2, output_size=1)
    model.to(device)

    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

    # 训练模型
    print("\n开始训练...")
    losses = train_model(model, train_loader, criterion, optimizer, 50, device)

    # 评估模型
    test_loss, _ = evaluate_model(model, test_loader, criterion, device)
    print(f"\n测试损失: {test_loss:.4f}")

    # 可视化结果
    model.eval()
    with torch.no_grad():
        # 取一个测试样本进行预测
        sample_x = X_test[:5].to(device)
        sample_y = y_test[:5]
        predictions = model(sample_x).cpu()

        print("\n预测结果对比:")
        for i in range(5):
            print(f"真实值: {sample_y[i]:.4f}, 预测值: {predictions[i].item():.4f}")


def test_classification_task():
    """测试分类任务"""
    print("\n" + "=" * 50)
    print("测试2: LSTM分类任务")
    print("=" * 50)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 生成数据
    seq_length = 15
    X, y = generate_sequence_classification_data(seq_length, 1000, num_classes=3)

    # 转换为张量
    X = torch.FloatTensor(X)
    y = torch.LongTensor(y)

    # 划分训练集和测试集
    train_size = int(0.8 * len(X))
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # 创建数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    test_dataset = TensorDataset(X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    # 创建模型
    model = SimpleLSTM(input_size=1, hidden_size=32, num_layers=1, output_size=3)
    model.to(device)

    # 损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    print(f"模型参数数量: {sum(p.numel() for p in model.parameters())}")

    # 训练模型
    print("\n开始训练...")
    losses = train_model(model, train_loader, criterion, optimizer, 30, device)

    # 评估模型
    test_loss, accuracy = evaluate_model(model, test_loader, criterion, device)
    print(f"\n测试损失: {test_loss:.4f}")
    print(f"测试准确率: {accuracy:.2f}%")


def test_bidirectional_lstm():
    """测试双向LSTM"""
    print("\n" + "=" * 50)
    print("测试3: 双向LSTM")
    print("=" * 50)

    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 生成数据
    seq_length = 20
    X, y = generate_sine_data(seq_length, 800, noise_level=0.05)

    # 转换为张量
    X = torch.FloatTensor(X).unsqueeze(-1)
    y = torch.FloatTensor(y)

    # 划分训练集和测试集
    train_size = int(0.8 * len(X))
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # 创建数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    test_dataset = TensorDataset(X_test, y_test)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    # 创建双向LSTM模型
    model = BiLSTM(input_size=1, hidden_size=32, num_layers=2, output_size=1)
    model.to(device)

    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    print(f"双向LSTM模型参数数量: {sum(p.numel() for p in model.parameters())}")

    # 训练模型
    print("\n开始训练...")
    losses = train_model(model, train_loader, criterion, optimizer, 40, device)

    # 评估模型
    test_loss, _ = evaluate_model(model, test_loader, criterion, device)
    print(f"\n测试损失: {test_loss:.4f}")


def test_lstm_shapes():
    """测试LSTM的输入输出形状"""
    print("\n" + "=" * 50)
    print("测试4: LSTM形状测试")
    print("=" * 50)

    # 创建测试数据
    batch_size = 4
    seq_length = 10
    input_size = 3
    hidden_size = 16
    num_layers = 2

    # 创建模型
    model = SimpleLSTM(input_size, hidden_size, num_layers, output_size=1)

    # 创建随机输入
    x = torch.randn(batch_size, seq_length, input_size)

    print(f"输入形状: {x.shape}")

    # 前向传播
    output = model(x)

    print(f"输出形状: {output.shape}")
    print(f"期望输出形状: ({batch_size}, 1)")

    # 测试双向LSTM
    bi_model = BiLSTM(input_size, hidden_size, num_layers, output_size=5)
    bi_output = bi_model(x)

    print(f"双向LSTM输出形状: {bi_output.shape}")
    print(f"期望双向LSTM输出形状: ({batch_size}, 5)")

    print("形状测试通过!")


class TestLSTM(unittest.TestCase):
    """LSTM单元测试"""

    def setUp(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    def test_simple_lstm_forward(self):
        """测试简单LSTM前向传播"""
        model = SimpleLSTM(input_size=5, hidden_size=10, num_layers=1, output_size=3)
        x = torch.randn(2, 8, 5)  # (batch_size, seq_length, input_size)
        output = model(x)
        self.assertEqual(output.shape, (2, 3))

    def test_bilstm_forward(self):
        """测试双向LSTM前向传播"""
        model = BiLSTM(input_size=5, hidden_size=10, num_layers=1, output_size=3)
        x = torch.randn(2, 8, 5)
        output = model(x)
        self.assertEqual(output.shape, (2, 3))

    def test_gradient_flow(self):
        """测试梯度流"""
        model = SimpleLSTM(input_size=1, hidden_size=5, num_layers=1, output_size=1)
        x = torch.randn(1, 5, 1, requires_grad=True)
        output = model(x)
        loss = output.sum()
        loss.backward()

        # 检查梯度是否存在
        for param in model.parameters():
            self.assertIsNotNone(param.grad)


if __name__ == "__main__":
    print("PyTorch版本:", torch.__version__)
    print("CUDA可用:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("CUDA版本:", torch.version.cuda)
        print("GPU设备:", torch.cuda.get_device_name(0))

    # 运行测试
    test_regression_task()
    test_classification_task()
    test_bidirectional_lstm()
    test_lstm_shapes()

    # 运行单元测试
    print("\n" + "=" * 50)
    print("运行单元测试")
    print("=" * 50)
    unittest.main(argv=[''], exit=False, verbosity=2)